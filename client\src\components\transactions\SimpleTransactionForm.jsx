import { useState } from 'react';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';

const SimpleTransactionForm = ({ onSubmit, onClose }) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const {
    register,
    handleSubmit,
    watch,
    reset,
    formState: { errors }
  } = useForm({
    defaultValues: {
      type: 'expense',
      amount: '',
      category: '',
      description: '',
      date: new Date().toISOString().split('T')[0]
    }
  });

  const watchType = watch('type');

  const categories = {
    income: [
      { value: 'salary', label: '💼 Salary' },
      { value: 'freelance', label: '💻 Freelance' },
      { value: 'business', label: '🏢 Business' },
      { value: 'investment', label: '📈 Investment' },
      { value: 'gift', label: '🎁 Gift' },
      { value: 'other-income', label: '💰 Other Income' }
    ],
    expense: [
      { value: 'food', label: '🍔 Food & Dining' },
      { value: 'transportation', label: '🚗 Transportation' },
      { value: 'housing', label: '🏠 Housing' },
      { value: 'utilities', label: '⚡ Utilities' },
      { value: 'healthcare', label: '🏥 Healthcare' },
      { value: 'entertainment', label: '🎬 Entertainment' },
      { value: 'shopping', label: '🛍️ Shopping' },
      { value: 'education', label: '📚 Education' },
      { value: 'travel', label: '✈️ Travel' },
      { value: 'other-expense', label: '💸 Other Expense' }
    ]
  };

  const onFormSubmit = async (data) => {
    setIsSubmitting(true);
    try {
      await onSubmit({
        ...data,
        amount: parseFloat(data.amount)
      });
      
      reset();
      toast.success('Transaction added successfully!');
      if (onClose) onClose();
    } catch (error) {
      toast.error('Failed to save transaction');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-900">
            Add New Transaction
          </h2>
          <button
            type="button"
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            ✕
          </button>
        </div>

        <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-4">
          {/* Type Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Transaction Type
            </label>
            <div className="grid grid-cols-2 gap-3">
              <label className="relative">
                <input
                  {...register('type', { required: 'Transaction type is required' })}
                  type="radio"
                  value="expense"
                  className="sr-only peer"
                />
                <div className="flex items-center justify-center p-3 border-2 border-gray-200 rounded-lg cursor-pointer peer-checked:border-red-500 peer-checked:bg-red-50 transition-all">
                  <span className="text-red-600 font-medium">💸 Expense</span>
                </div>
              </label>
              <label className="relative">
                <input
                  {...register('type', { required: 'Transaction type is required' })}
                  type="radio"
                  value="income"
                  className="sr-only peer"
                />
                <div className="flex items-center justify-center p-3 border-2 border-gray-200 rounded-lg cursor-pointer peer-checked:border-green-500 peer-checked:bg-green-50 transition-all">
                  <span className="text-green-600 font-medium">💰 Income</span>
                </div>
              </label>
            </div>
            {errors.type && (
              <p className="mt-1 text-sm text-red-600">{errors.type.message}</p>
            )}
          </div>

          {/* Amount */}
          <div>
            <label htmlFor="amount" className="block text-sm font-medium text-gray-700 mb-1">
              Amount
            </label>
            <input
              {...register('amount', {
                required: 'Amount is required',
                min: { value: 0.01, message: 'Amount must be greater than 0' }
              })}
              type="number"
              step="0.01"
              min="0.01"
              className={`input ${errors.amount ? 'border-red-500' : ''}`}
              placeholder="0.00"
            />
            {errors.amount && (
              <p className="mt-1 text-sm text-red-600">{errors.amount.message}</p>
            )}
          </div>

          {/* Category */}
          <div>
            <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
              Category
            </label>
            <select
              {...register('category', { required: 'Category is required' })}
              className={`input ${errors.category ? 'border-red-500' : ''}`}
            >
              <option value="">Select a category</option>
              {categories[watchType]?.map((cat) => (
                <option key={cat.value} value={cat.value}>
                  {cat.label}
                </option>
              ))}
            </select>
            {errors.category && (
              <p className="mt-1 text-sm text-red-600">{errors.category.message}</p>
            )}
          </div>

          {/* Description */}
          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <input
              {...register('description', {
                required: 'Description is required',
                maxLength: { value: 200, message: 'Description cannot exceed 200 characters' }
              })}
              type="text"
              className={`input ${errors.description ? 'border-red-500' : ''}`}
              placeholder="Enter description..."
              maxLength={200}
            />
            {errors.description && (
              <p className="mt-1 text-sm text-red-600">{errors.description.message}</p>
            )}
          </div>

          {/* Date */}
          <div>
            <label htmlFor="date" className="block text-sm font-medium text-gray-700 mb-1">
              Date
            </label>
            <input
              {...register('date', { required: 'Date is required' })}
              type="date"
              className={`input ${errors.date ? 'border-red-500' : ''}`}
            />
            {errors.date && (
              <p className="mt-1 text-sm text-red-600">{errors.date.message}</p>
            )}
          </div>

          {/* Submit Button */}
          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="btn-secondary"
              disabled={isSubmitting}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="btn-primary"
            >
              {isSubmitting ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Adding...
                </div>
              ) : (
                'Add Transaction'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default SimpleTransactionForm;
