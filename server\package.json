{"name": "splitify-server", "version": "1.0.0", "description": "Express backend for Splitify - expense tracker and splitter", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js"}, "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "helmet": "^7.1.0", "morgan": "^1.10.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "socket.io": "^4.7.4", "multer": "^1.4.5-lts.1", "axios": "^1.6.2", "express-validator": "^7.0.1", "express-rate-limit": "^7.1.5"}, "devDependencies": {"nodemon": "^3.0.2"}}