#root {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

.App-header {
  padding: 20px;
}

.status-section {
  margin: 2rem 0;
  padding: 1rem;
  border: 1px solid #ccc;
  border-radius: 8px;
  background-color: rgba(255, 255, 255, 0.05);
}

.success {
  color: #4caf50;
}

.error {
  color: #f44336;
}

.api-data {
  margin-top: 1rem;
  text-align: left;
}

.api-data pre {
  background-color: #1a1a1a;
  padding: 1rem;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 0.9em;
}

.actions {
  margin: 2rem 0;
}

.actions button {
  margin: 0 0.5rem;
}

.tech-stack {
  margin-top: 2rem;
  padding: 1rem;
  border: 1px solid #646cff;
  border-radius: 8px;
  background-color: rgba(100, 108, 255, 0.1);
}

.tech-stack ul {
  list-style-type: none;
  padding: 0;
}

.tech-stack li {
  margin: 0.5rem 0;
  padding: 0.5rem;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
