#root {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
}

.App-header {
  padding: 1rem;
  text-align: center;
}

.App-header h1 {
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, #646cff, #535bf2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.App-header p {
  margin-bottom: 2rem;
  opacity: 0.8;
}

.error-banner {
  background: rgba(244, 67, 54, 0.1);
  border: 1px solid rgba(244, 67, 54, 0.3);
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.error-banner p {
  margin: 0;
  color: #f44336;
}

.error-banner button {
  background: #f44336;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
}

.error-banner button:hover {
  background: #d32f2f;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  #root {
    padding: 0.5rem;
  }

  .App-header {
    padding: 0.5rem;
  }

  .error-banner {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
}
