const express = require('express');
const multer = require('multer');
const axios = require('axios');
const fs = require('fs');
const path = require('path');
const { protect } = require('../middleware/auth');
const Transaction = require('../models/Transaction');

const router = express.Router();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadPath = path.join(__dirname, '../uploads');
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }
    cb(null, uploadPath);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, `bill-${uniqueSuffix}${path.extname(file.originalname)}`);
  }
});

const fileFilter = (req, file, cb) => {
  // Accept only image files
  if (file.mimetype.startsWith('image/')) {
    cb(null, true);
  } else {
    cb(new Error('Only image files are allowed'), false);
  }
};

const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE) || 5 * 1024 * 1024 // 5MB default
  }
});

// Helper function to convert image to base64
const imageToBase64 = (filePath) => {
  const imageBuffer = fs.readFileSync(filePath);
  return imageBuffer.toString('base64');
};

// Helper function to call Gemini API
const analyzeImageWithGemini = async (base64Image, mimeType) => {
  try {
    const response = await axios.post(
      `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${process.env.GEMINI_API_KEY}`,
      {
        contents: [{
          parts: [
            {
              text: `Analyze this receipt/bill image and extract the following information in JSON format:
              {
                "amount": <total amount as number>,
                "date": "<date in YYYY-MM-DD format>",
                "merchant": "<merchant/store name>",
                "category": "<expense category>",
                "items": [<array of items if visible>],
                "currency": "<currency symbol or code>"
              }
              
              For category, choose from: food, transportation, housing, utilities, healthcare, entertainment, shopping, education, travel, other-expense
              
              If you cannot clearly identify any field, use null for that field.
              Only return the JSON object, no additional text.`
            },
            {
              inline_data: {
                mime_type: mimeType,
                data: base64Image
              }
            }
          ]
        }],
        generationConfig: {
          temperature: 0.1,
          topK: 32,
          topP: 1,
          maxOutputTokens: 1024,
        }
      },
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );

    const generatedText = response.data.candidates[0].content.parts[0].text;
    
    // Clean up the response to extract JSON
    const jsonMatch = generatedText.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      return JSON.parse(jsonMatch[0]);
    }
    
    throw new Error('Could not parse JSON from Gemini response');
  } catch (error) {
    console.error('Gemini API error:', error.response?.data || error.message);
    throw new Error('Failed to analyze image with Gemini API');
  }
};

// Helper function to clean up uploaded file
const cleanupFile = (filePath) => {
  try {
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }
  } catch (error) {
    console.error('Error cleaning up file:', error);
  }
};

// @route   POST /api/bill-scan/upload
// @desc    Upload and analyze bill image
// @access  Private
router.post('/upload', protect, upload.single('bill'), async (req, res) => {
  let filePath = null;
  
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'No image file uploaded'
      });
    }

    filePath = req.file.path;
    const mimeType = req.file.mimetype;

    // Convert image to base64
    const base64Image = imageToBase64(filePath);

    // Analyze with Gemini API
    const extractedData = await analyzeImageWithGemini(base64Image, mimeType);

    // Validate and process extracted data
    const processedData = {
      amount: extractedData.amount || null,
      date: extractedData.date || new Date().toISOString().split('T')[0],
      merchant: extractedData.merchant || 'Unknown Merchant',
      category: extractedData.category || 'other-expense',
      items: extractedData.items || [],
      currency: extractedData.currency || 'USD',
      description: `Bill from ${extractedData.merchant || 'Unknown Merchant'}`,
      type: 'expense'
    };

    res.json({
      success: true,
      message: 'Bill analyzed successfully',
      data: {
        extracted: processedData,
        originalFilename: req.file.originalname,
        fileSize: req.file.size
      }
    });

  } catch (error) {
    console.error('Bill scan error:', error);
    
    let message = 'Failed to analyze bill';
    if (error.message.includes('Gemini API')) {
      message = 'Failed to analyze image. Please try with a clearer image.';
    } else if (error.message.includes('JSON')) {
      message = 'Could not extract structured data from the image.';
    }

    res.status(500).json({
      success: false,
      message,
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  } finally {
    // Clean up uploaded file
    if (filePath) {
      cleanupFile(filePath);
    }
  }
});

// @route   POST /api/bill-scan/save-transaction
// @desc    Save extracted bill data as transaction
// @access  Private
router.post('/save-transaction', protect, async (req, res) => {
  try {
    const { amount, category, description, date, merchant, items } = req.body;

    if (!amount || amount <= 0) {
      return res.status(400).json({
        success: false,
        message: 'Valid amount is required'
      });
    }

    // Create transaction
    const transaction = new Transaction({
      userId: req.user._id,
      amount: parseFloat(amount),
      category: category || 'other-expense',
      description: description || `Bill from ${merchant || 'Unknown Merchant'}`,
      date: date ? new Date(date) : new Date(),
      type: 'expense',
      // Store additional bill scan metadata
      metadata: {
        source: 'bill-scan',
        merchant,
        items: items || []
      }
    });

    const savedTransaction = await transaction.save();

    // Get updated balance
    const balance = await Transaction.getUserBalance(req.user._id);

    res.status(201).json({
      success: true,
      message: 'Transaction created from bill scan',
      data: {
        transaction: savedTransaction,
        balance
      }
    });

  } catch (error) {
    console.error('Save transaction error:', error);
    
    if (error.name === 'ValidationError') {
      const errors = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to save transaction',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   GET /api/bill-scan/history
// @desc    Get bill scan history
// @access  Private
router.get('/history', protect, async (req, res) => {
  try {
    const { limit = 20, page = 1 } = req.query;
    const skip = (page - 1) * limit;

    const billTransactions = await Transaction.find({
      userId: req.user._id,
      'metadata.source': 'bill-scan'
    })
    .sort({ createdAt: -1 })
    .limit(parseInt(limit))
    .skip(skip);

    const total = await Transaction.countDocuments({
      userId: req.user._id,
      'metadata.source': 'bill-scan'
    });

    res.json({
      success: true,
      data: {
        transactions: billTransactions,
        pagination: {
          current: parseInt(page),
          total: Math.ceil(total / limit),
          count: billTransactions.length,
          totalRecords: total
        }
      }
    });

  } catch (error) {
    console.error('Get bill history error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch bill scan history',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

module.exports = router;
