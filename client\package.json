{"name": "splitify-client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "axios": "^1.6.2", "react-router-dom": "^6.20.1", "socket.io-client": "^4.7.4", "chart.js": "^4.4.0", "react-chartjs-2": "^5.2.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "lucide-react": "^0.294.0", "@headlessui/react": "^1.7.17"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "vite": "^5.0.8", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32"}}