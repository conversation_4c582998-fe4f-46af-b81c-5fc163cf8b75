const express = require('express');
const router = express.Router();
const Transaction = require('../models/Transaction');

// @route   GET /api/transactions
// @desc    Get all transactions for a user
// @access  Public (will be protected later with auth)
router.get('/', async (req, res) => {
  try {
    const { userId = 'default-user', type, category, limit = 50, page = 1 } = req.query;
    
    // Build filter object
    const filter = { userId };
    if (type) filter.type = type;
    if (category) filter.category = category;
    
    // Calculate pagination
    const skip = (page - 1) * limit;
    
    // Get transactions with pagination
    const transactions = await Transaction.find(filter)
      .sort({ date: -1, createdAt: -1 })
      .limit(parseInt(limit))
      .skip(skip);
    
    // Get total count for pagination
    const total = await Transaction.countDocuments(filter);
    
    // Get user balance
    const balance = await Transaction.getUserBalance(userId);
    
    res.json({
      success: true,
      data: {
        transactions,
        pagination: {
          current: parseInt(page),
          total: Math.ceil(total / limit),
          count: transactions.length,
          totalRecords: total
        },
        balance
      }
    });
  } catch (error) {
    console.error('Error fetching transactions:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching transactions',
      error: error.message
    });
  }
});

// @route   GET /api/transactions/:id
// @desc    Get single transaction
// @access  Public
router.get('/:id', async (req, res) => {
  try {
    const transaction = await Transaction.findById(req.params.id);
    
    if (!transaction) {
      return res.status(404).json({
        success: false,
        message: 'Transaction not found'
      });
    }
    
    res.json({
      success: true,
      data: transaction
    });
  } catch (error) {
    console.error('Error fetching transaction:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching transaction',
      error: error.message
    });
  }
});

// @route   POST /api/transactions
// @desc    Create new transaction
// @access  Public
router.post('/', async (req, res) => {
  try {
    const { amount, category, description, date, type, userId = 'default-user' } = req.body;
    
    // Validate required fields
    if (!amount || !category || !description || !type) {
      return res.status(400).json({
        success: false,
        message: 'Please provide amount, category, description, and type'
      });
    }
    
    // Create new transaction
    const transaction = new Transaction({
      userId,
      amount: parseFloat(amount),
      category,
      description,
      date: date ? new Date(date) : new Date(),
      type
    });
    
    const savedTransaction = await transaction.save();
    
    // Get updated balance
    const balance = await Transaction.getUserBalance(userId);
    
    res.status(201).json({
      success: true,
      message: 'Transaction created successfully',
      data: {
        transaction: savedTransaction,
        balance
      }
    });
  } catch (error) {
    console.error('Error creating transaction:', error);
    
    // Handle validation errors
    if (error.name === 'ValidationError') {
      const errors = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors
      });
    }
    
    res.status(500).json({
      success: false,
      message: 'Error creating transaction',
      error: error.message
    });
  }
});

// @route   PUT /api/transactions/:id
// @desc    Update transaction
// @access  Public
router.put('/:id', async (req, res) => {
  try {
    const { amount, category, description, date, type } = req.body;
    
    // Find transaction
    const transaction = await Transaction.findById(req.params.id);
    
    if (!transaction) {
      return res.status(404).json({
        success: false,
        message: 'Transaction not found'
      });
    }
    
    // Update fields
    if (amount !== undefined) transaction.amount = parseFloat(amount);
    if (category) transaction.category = category;
    if (description) transaction.description = description;
    if (date) transaction.date = new Date(date);
    if (type) transaction.type = type;
    
    const updatedTransaction = await transaction.save();
    
    // Get updated balance
    const balance = await Transaction.getUserBalance(transaction.userId);
    
    res.json({
      success: true,
      message: 'Transaction updated successfully',
      data: {
        transaction: updatedTransaction,
        balance
      }
    });
  } catch (error) {
    console.error('Error updating transaction:', error);
    
    // Handle validation errors
    if (error.name === 'ValidationError') {
      const errors = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors
      });
    }
    
    res.status(500).json({
      success: false,
      message: 'Error updating transaction',
      error: error.message
    });
  }
});

// @route   DELETE /api/transactions/:id
// @desc    Delete transaction
// @access  Public
router.delete('/:id', async (req, res) => {
  try {
    const transaction = await Transaction.findById(req.params.id);
    
    if (!transaction) {
      return res.status(404).json({
        success: false,
        message: 'Transaction not found'
      });
    }
    
    const userId = transaction.userId;
    await Transaction.findByIdAndDelete(req.params.id);
    
    // Get updated balance
    const balance = await Transaction.getUserBalance(userId);
    
    res.json({
      success: true,
      message: 'Transaction deleted successfully',
      data: { balance }
    });
  } catch (error) {
    console.error('Error deleting transaction:', error);
    res.status(500).json({
      success: false,
      message: 'Error deleting transaction',
      error: error.message
    });
  }
});

// @route   GET /api/transactions/stats/summary
// @desc    Get transaction statistics
// @access  Public
router.get('/stats/summary', async (req, res) => {
  try {
    const { userId = 'default-user' } = req.query;
    
    // Get balance
    const balance = await Transaction.getUserBalance(userId);
    
    // Get category breakdown for expenses
    const expenseBreakdown = await Transaction.getCategoryBreakdown(userId, 'expense');
    
    // Get category breakdown for income
    const incomeBreakdown = await Transaction.getCategoryBreakdown(userId, 'income');
    
    // Get recent transactions count
    const recentCount = await Transaction.countDocuments({
      userId,
      date: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) }
    });
    
    res.json({
      success: true,
      data: {
        balance,
        expenseBreakdown,
        incomeBreakdown,
        recentTransactionsCount: recentCount
      }
    });
  } catch (error) {
    console.error('Error fetching statistics:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching statistics',
      error: error.message
    });
  }
});

module.exports = router;
