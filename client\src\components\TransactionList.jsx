import { useState } from 'react'
import './TransactionList.css'

const TransactionList = ({ transactions, onDelete, onEdit, balance, loading }) => {
  const [filter, setFilter] = useState('all')
  const [sortBy, setSortBy] = useState('date')
  const [sortOrder, setSortOrder] = useState('desc')

  const categoryLabels = {
    // Income categories
    'salary': '💼 Salary',
    'freelance': '💻 Freelance',
    'business': '🏢 Business',
    'investment': '📈 Investment',
    'gift': '🎁 Gift',
    'other-income': '💰 Other Income',
    // Expense categories
    'food': '🍔 Food & Dining',
    'transportation': '🚗 Transportation',
    'housing': '🏠 Housing',
    'utilities': '⚡ Utilities',
    'healthcare': '🏥 Healthcare',
    'entertainment': '🎬 Entertainment',
    'shopping': '🛍️ Shopping',
    'education': '📚 Education',
    'travel': '✈️ Travel',
    'other-expense': '💸 Other Expense'
  }

  const filteredTransactions = transactions
    .filter(transaction => {
      if (filter === 'all') return true
      return transaction.type === filter
    })
    .sort((a, b) => {
      let aValue, bValue
      
      switch (sortBy) {
        case 'amount':
          aValue = a.amount
          bValue = b.amount
          break
        case 'category':
          aValue = a.category
          bValue = b.category
          break
        case 'date':
        default:
          aValue = new Date(a.date)
          bValue = new Date(b.date)
      }
      
      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1
      } else {
        return aValue < bValue ? 1 : -1
      }
    })

  const formatDate = (dateString) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const formatAmount = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  const handleDelete = async (id, description) => {
    if (window.confirm(`Are you sure you want to delete "${description}"?`)) {
      await onDelete(id)
    }
  }

  if (loading) {
    return (
      <div className="transaction-list-container">
        <div className="loading">Loading transactions...</div>
      </div>
    )
  }

  return (
    <div className="transaction-list-container">
      {/* Balance Summary */}
      {balance && (
        <div className="balance-summary">
          <div className="balance-item income">
            <span className="label">Total Income</span>
            <span className="amount">{formatAmount(balance.income)}</span>
          </div>
          <div className="balance-item expense">
            <span className="label">Total Expenses</span>
            <span className="amount">{formatAmount(balance.expense)}</span>
          </div>
          <div className={`balance-item balance ${balance.balance >= 0 ? 'positive' : 'negative'}`}>
            <span className="label">Net Balance</span>
            <span className="amount">{formatAmount(balance.balance)}</span>
          </div>
        </div>
      )}

      {/* Filters and Controls */}
      <div className="list-controls">
        <div className="filters">
          <select
            value={filter}
            onChange={(e) => setFilter(e.target.value)}
            className="filter-select"
          >
            <option value="all">All Transactions</option>
            <option value="income">💰 Income Only</option>
            <option value="expense">💸 Expenses Only</option>
          </select>

          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className="sort-select"
          >
            <option value="date">Sort by Date</option>
            <option value="amount">Sort by Amount</option>
            <option value="category">Sort by Category</option>
          </select>

          <button
            onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
            className="sort-order-btn"
            title={`Currently: ${sortOrder === 'asc' ? 'Ascending' : 'Descending'}`}
          >
            {sortOrder === 'asc' ? '↑' : '↓'}
          </button>
        </div>

        <div className="transaction-count">
          {filteredTransactions.length} transaction{filteredTransactions.length !== 1 ? 's' : ''}
        </div>
      </div>

      {/* Transaction List */}
      {filteredTransactions.length === 0 ? (
        <div className="empty-state">
          <p>No transactions found.</p>
          <p>Add your first transaction using the form above!</p>
        </div>
      ) : (
        <div className="transaction-list">
          {filteredTransactions.map((transaction) => (
            <div
              key={transaction._id}
              className={`transaction-item ${transaction.type}`}
            >
              <div className="transaction-main">
                <div className="transaction-info">
                  <div className="category">
                    {categoryLabels[transaction.category] || transaction.category}
                  </div>
                  <div className="description">{transaction.description}</div>
                  <div className="date">{formatDate(transaction.date)}</div>
                </div>
                
                <div className="transaction-amount">
                  <span className={`amount ${transaction.type}`}>
                    {transaction.type === 'income' ? '+' : '-'}{formatAmount(transaction.amount)}
                  </span>
                </div>
              </div>

              <div className="transaction-actions">
                <button
                  onClick={() => onEdit(transaction)}
                  className="edit-btn"
                  title="Edit transaction"
                >
                  ✏️
                </button>
                <button
                  onClick={() => handleDelete(transaction._id, transaction.description)}
                  className="delete-btn"
                  title="Delete transaction"
                >
                  🗑️
                </button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

export default TransactionList
