.transaction-list-container {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
  backdrop-filter: blur(10px);
}

/* Balance Summary */
.balance-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 8px;
}

.balance-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  border-radius: 8px;
  text-align: center;
}

.balance-item.income {
  background: rgba(76, 175, 80, 0.1);
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.balance-item.expense {
  background: rgba(244, 67, 54, 0.1);
  border: 1px solid rgba(244, 67, 54, 0.3);
}

.balance-item.balance.positive {
  background: rgba(76, 175, 80, 0.15);
  border: 1px solid rgba(76, 175, 80, 0.4);
}

.balance-item.balance.negative {
  background: rgba(244, 67, 54, 0.15);
  border: 1px solid rgba(244, 67, 54, 0.4);
}

.balance-item .label {
  font-size: 0.9rem;
  opacity: 0.8;
  margin-bottom: 0.5rem;
}

.balance-item .amount {
  font-size: 1.5rem;
  font-weight: bold;
}

/* List Controls */
.list-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.filters {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.filter-select,
.sort-select {
  padding: 0.5rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.05);
  color: inherit;
  font-size: 0.9rem;
}

.sort-order-btn {
  padding: 0.5rem 0.75rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.05);
  color: inherit;
  cursor: pointer;
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

.sort-order-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

.transaction-count {
  font-size: 0.9rem;
  opacity: 0.7;
}

/* Transaction List */
.transaction-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.transaction-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid;
  background: rgba(255, 255, 255, 0.03);
  transition: all 0.3s ease;
}

.transaction-item:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(-1px);
}

.transaction-item.income {
  border-left-color: #4caf50;
}

.transaction-item.expense {
  border-left-color: #f44336;
}

.transaction-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex: 1;
  margin-right: 1rem;
}

.transaction-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.category {
  font-weight: 500;
  font-size: 1rem;
}

.description {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
}

.date {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.8rem;
}

.transaction-amount .amount {
  font-size: 1.2rem;
  font-weight: bold;
}

.transaction-amount .amount.income {
  color: #4caf50;
}

.transaction-amount .amount.expense {
  color: #f44336;
}

.transaction-actions {
  display: flex;
  gap: 0.5rem;
}

.edit-btn,
.delete-btn {
  padding: 0.5rem;
  border: none;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.edit-btn:hover {
  background: rgba(100, 108, 255, 0.3);
}

.delete-btn:hover {
  background: rgba(244, 67, 54, 0.3);
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 3rem 1rem;
  color: rgba(255, 255, 255, 0.6);
}

.empty-state p {
  margin: 0.5rem 0;
}

/* Loading State */
.loading {
  text-align: center;
  padding: 2rem;
  color: rgba(255, 255, 255, 0.7);
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .transaction-list-container {
    padding: 1rem;
  }
  
  .balance-summary {
    grid-template-columns: 1fr;
  }
  
  .list-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filters {
    justify-content: center;
  }
  
  .transaction-item {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }
  
  .transaction-main {
    margin-right: 0;
  }
  
  .transaction-info {
    text-align: center;
  }
  
  .transaction-actions {
    justify-content: center;
  }
}

/* Light Mode */
@media (prefers-color-scheme: light) {
  .transaction-list-container {
    background: rgba(0, 0, 0, 0.02);
    border: 1px solid rgba(0, 0, 0, 0.1);
  }
  
  .balance-summary {
    background: rgba(0, 0, 0, 0.01);
  }
  
  .filter-select,
  .sort-select,
  .sort-order-btn {
    background: rgba(0, 0, 0, 0.02);
    border: 1px solid rgba(0, 0, 0, 0.2);
    color: #213547;
  }
  
  .transaction-item {
    background: rgba(0, 0, 0, 0.01);
  }
  
  .transaction-item:hover {
    background: rgba(0, 0, 0, 0.05);
  }
  
  .description {
    color: rgba(0, 0, 0, 0.7);
  }
  
  .date {
    color: rgba(0, 0, 0, 0.5);
  }
  
  .transaction-count {
    color: rgba(0, 0, 0, 0.6);
  }
  
  .empty-state {
    color: rgba(0, 0, 0, 0.5);
  }
  
  .loading {
    color: rgba(0, 0, 0, 0.6);
  }
  
  .edit-btn,
  .delete-btn {
    background: rgba(0, 0, 0, 0.05);
  }
}
