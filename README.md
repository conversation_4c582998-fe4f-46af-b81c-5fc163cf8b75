# 🏦 Expense Tracker - MERN Stack

A full-stack expense tracking application built with the MERN stack (MongoDB, Express.js, React, Node.js).

## 🚀 Features

- **Frontend**: React with Vite for fast development
- **Backend**: Express.js REST API
- **Database**: MongoDB with Mongoose ODM
- **Proxy Setup**: Vite dev server proxy for seamless frontend-backend communication
- **Environment Configuration**: Separate .env files for client and server
- **CORS**: Configured for cross-origin requests
- **Security**: Helmet.js for security headers
- **Logging**: Morgan for HTTP request logging

## 📁 Project Structure

```
expense_tracker/
├── client/                 # React frontend
│   ├── public/
│   ├── src/
│   │   ├── App.jsx
│   │   ├── App.css
│   │   ├── index.css
│   │   └── main.jsx
│   ├── .env
│   ├── .env.example
│   ├── index.html
│   ├── package.json
│   └── vite.config.js
├── server/                 # Express backend
│   ├── .env
│   ├── .env.example
│   ├── index.js
│   └── package.json
├── package.json           # Root package.json with scripts
└── README.md
```

## 🛠️ Prerequisites

Before running this application, make sure you have the following installed:

- **Node.js** (v16 or higher)
- **npm** or **yarn**
- **MongoDB** (local installation or MongoDB Atlas)

## ⚡ Quick Start

### 1. Clone and Install Dependencies

```bash
# Install root dependencies
npm install

# Install all dependencies (root, client, and server)
npm run install-all
```

### 2. Environment Setup

Copy the example environment files and configure them:

```bash
# Server environment
cp server/.env.example server/.env

# Client environment  
cp client/.env.example client/.env
```

**Configure server/.env:**
```env
PORT=5000
NODE_ENV=development
MONGODB_URI=mongodb://localhost:27017/expense_tracker
CLIENT_URL=http://localhost:5173
JWT_SECRET=your_jwt_secret_key_here_change_in_production
```

**Configure client/.env:**
```env
VITE_API_URL=http://localhost:5000
VITE_APP_NAME=Expense Tracker
```

### 3. Start MongoDB

Make sure MongoDB is running on your system:

```bash
# If using local MongoDB
mongod

# Or if using MongoDB as a service
sudo systemctl start mongod
```

### 4. Run the Application

```bash
# Start both frontend and backend concurrently
npm run dev
```

This will start:
- **Backend server** on http://localhost:5000
- **Frontend development server** on http://localhost:5173

## 📜 Available Scripts

### Root Level Scripts
- `npm run dev` - Start both client and server in development mode
- `npm run install-all` - Install dependencies for root, client, and server
- `npm run build` - Build the client for production
- `npm start` - Start the server in production mode

### Server Scripts
- `npm run server` - Start only the backend server
- `npm run install-server` - Install only server dependencies

### Client Scripts  
- `npm run client` - Start only the frontend development server
- `npm run install-client` - Install only client dependencies

## 🔌 API Endpoints

The backend provides the following endpoints:

- `GET /` - API information and available endpoints
- `GET /api/health` - Health check endpoint with database status
- `GET /api/test` - Test endpoint for frontend-backend connectivity

## 🌐 Frontend-Backend Communication

The frontend communicates with the backend through:

1. **Vite Proxy Configuration**: Requests to `/api/*` are proxied to `http://localhost:5000`
2. **Axios HTTP Client**: For making API requests
3. **CORS Configuration**: Backend configured to accept requests from frontend origin

## 🗄️ Database

The application uses MongoDB with Mongoose ODM:

- **Database Name**: `expense_tracker`
- **Connection**: Configured via `MONGODB_URI` environment variable
- **Default Local URI**: `mongodb://localhost:27017/expense_tracker`

## 🔧 Development

### Adding New API Routes

1. Add routes in `server/index.js` or create separate route files
2. Follow RESTful conventions
3. Use appropriate HTTP methods (GET, POST, PUT, DELETE)

### Adding New React Components

1. Create components in `client/src/components/`
2. Import and use in `App.jsx` or other components
3. Follow React best practices and hooks

## 🚀 Deployment

### Backend Deployment
1. Set `NODE_ENV=production` in environment
2. Configure production MongoDB URI
3. Deploy to platforms like Heroku, Railway, or DigitalOcean

### Frontend Deployment
1. Run `npm run build` to create production build
2. Deploy the `client/dist` folder to platforms like Netlify, Vercel, or serve statically

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📝 License

This project is licensed under the MIT License.

## 🆘 Troubleshooting

### Common Issues

1. **MongoDB Connection Error**
   - Ensure MongoDB is running
   - Check the `MONGODB_URI` in server/.env

2. **Frontend Can't Connect to Backend**
   - Verify both servers are running
   - Check proxy configuration in `vite.config.js`
   - Ensure CORS is properly configured

3. **Port Already in Use**
   - Change ports in environment files
   - Kill processes using the ports

### Getting Help

If you encounter issues:
1. Check the console for error messages
2. Verify all dependencies are installed
3. Ensure environment variables are properly set
4. Check that MongoDB is running and accessible
