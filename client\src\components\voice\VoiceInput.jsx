import { useState, useRef, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>c<PERSON>ff, Volume2, AlertCircle } from 'lucide-react';
import toast from 'react-hot-toast';

const VoiceInput = ({ onTransactionParsed, onClose }) => {
  const [isListening, setIsListening] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [isSupported, setIsSupported] = useState(true);
  const [parsedData, setParsedData] = useState(null);
  const recognitionRef = useRef(null);

  useEffect(() => {
    // Check if Web Speech API is supported
    if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
      setIsSupported(false);
      return;
    }

    // Initialize speech recognition
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    recognitionRef.current = new SpeechRecognition();
    
    recognitionRef.current.continuous = false;
    recognitionRef.current.interimResults = true;
    recognitionRef.current.lang = 'en-US';

    recognitionRef.current.onstart = () => {
      setIsListening(true);
      toast.success('Listening... Speak now!');
    };

    recognitionRef.current.onresult = (event) => {
      let finalTranscript = '';
      let interimTranscript = '';

      for (let i = event.resultIndex; i < event.results.length; i++) {
        const transcript = event.results[i][0].transcript;
        if (event.results[i].isFinal) {
          finalTranscript += transcript;
        } else {
          interimTranscript += transcript;
        }
      }

      setTranscript(finalTranscript || interimTranscript);

      if (finalTranscript) {
        parseVoiceInput(finalTranscript);
      }
    };

    recognitionRef.current.onerror = (event) => {
      console.error('Speech recognition error:', event.error);
      setIsListening(false);
      
      let errorMessage = 'Speech recognition error';
      switch (event.error) {
        case 'no-speech':
          errorMessage = 'No speech detected. Please try again.';
          break;
        case 'audio-capture':
          errorMessage = 'Microphone not accessible. Please check permissions.';
          break;
        case 'not-allowed':
          errorMessage = 'Microphone permission denied.';
          break;
        case 'network':
          errorMessage = 'Network error. Please check your connection.';
          break;
        default:
          errorMessage = `Speech recognition error: ${event.error}`;
      }
      
      toast.error(errorMessage);
    };

    recognitionRef.current.onend = () => {
      setIsListening(false);
    };

    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.stop();
      }
    };
  }, []);

  const parseVoiceInput = (text) => {
    try {
      const parsed = parseTransactionFromText(text);
      if (parsed) {
        setParsedData(parsed);
        toast.success('Transaction parsed successfully!');
      } else {
        toast.error('Could not parse transaction. Please try again with a clearer format.');
      }
    } catch (error) {
      console.error('Parse error:', error);
      toast.error('Failed to parse transaction');
    }
  };

  const parseTransactionFromText = (text) => {
    const lowerText = text.toLowerCase().trim();
    
    // Common patterns for voice input
    const patterns = [
      // "spent 50 on food today" or "spent 50 dollars on food"
      /(?:spent|spend)\s+(?:\$)?(\d+(?:\.\d{2})?)\s*(?:dollars?)?\s+on\s+(\w+)(?:\s+(?:today|yesterday))?/i,
      // "paid 100 for groceries" or "paid 100 dollars for groceries"
      /(?:paid|pay)\s+(?:\$)?(\d+(?:\.\d{2})?)\s*(?:dollars?)?\s+for\s+(\w+)/i,
      // "bought food for 25" or "bought food for 25 dollars"
      /(?:bought|buy)\s+(\w+)\s+for\s+(?:\$)?(\d+(?:\.\d{2})?)\s*(?:dollars?)?/i,
      // "earned 1000 from salary" or "received 500 from freelance"
      /(?:earned|earn|received|receive|got)\s+(?:\$)?(\d+(?:\.\d{2})?)\s*(?:dollars?)?\s+from\s+(\w+)/i,
      // "income 2000 salary" or "expense 50 food"
      /(income|expense)\s+(?:\$)?(\d+(?:\.\d{2})?)\s*(?:dollars?)?\s+(\w+)/i
    ];

    let amount, category, type = 'expense';
    
    for (const pattern of patterns) {
      const match = lowerText.match(pattern);
      if (match) {
        if (pattern.source.includes('income|expense')) {
          // Pattern 5: "income 2000 salary"
          type = match[1];
          amount = parseFloat(match[2]);
          category = match[3];
        } else if (pattern.source.includes('earned|earn|received|receive|got')) {
          // Pattern 4: "earned 1000 from salary"
          type = 'income';
          amount = parseFloat(match[1]);
          category = match[2];
        } else if (pattern.source.includes('bought|buy')) {
          // Pattern 3: "bought food for 25"
          amount = parseFloat(match[2]);
          category = match[1];
        } else {
          // Patterns 1 & 2: "spent 50 on food" or "paid 100 for groceries"
          amount = parseFloat(match[1]);
          category = match[2];
        }
        break;
      }
    }

    if (!amount || !category) {
      return null;
    }

    // Map common spoken categories to our system categories
    const categoryMapping = {
      // Food related
      'food': 'food',
      'groceries': 'food',
      'restaurant': 'food',
      'dining': 'food',
      'lunch': 'food',
      'dinner': 'food',
      'breakfast': 'food',
      'coffee': 'food',
      
      // Transportation
      'gas': 'transportation',
      'fuel': 'transportation',
      'uber': 'transportation',
      'taxi': 'transportation',
      'bus': 'transportation',
      'train': 'transportation',
      'car': 'transportation',
      
      // Housing
      'rent': 'housing',
      'mortgage': 'housing',
      'house': 'housing',
      'apartment': 'housing',
      
      // Utilities
      'electricity': 'utilities',
      'water': 'utilities',
      'internet': 'utilities',
      'phone': 'utilities',
      
      // Entertainment
      'movie': 'entertainment',
      'movies': 'entertainment',
      'game': 'entertainment',
      'games': 'entertainment',
      'netflix': 'entertainment',
      
      // Shopping
      'clothes': 'shopping',
      'clothing': 'shopping',
      'shoes': 'shopping',
      'amazon': 'shopping',
      
      // Income categories
      'salary': 'salary',
      'work': 'salary',
      'job': 'salary',
      'freelance': 'freelance',
      'freelancing': 'freelance',
      'business': 'business',
      'investment': 'investment',
      'stocks': 'investment',
      'gift': 'gift',
      'bonus': 'salary'
    };

    const mappedCategory = categoryMapping[category] || (type === 'income' ? 'other-income' : 'other-expense');

    return {
      amount,
      category: mappedCategory,
      type,
      description: `${type === 'income' ? 'Received' : 'Spent'} $${amount} on ${category} (Voice input)`,
      date: new Date().toISOString().split('T')[0]
    };
  };

  const startListening = () => {
    if (!isSupported) {
      toast.error('Speech recognition is not supported in this browser');
      return;
    }

    if (recognitionRef.current && !isListening) {
      setTranscript('');
      setParsedData(null);
      recognitionRef.current.start();
    }
  };

  const stopListening = () => {
    if (recognitionRef.current && isListening) {
      recognitionRef.current.stop();
    }
  };

  const handleConfirm = () => {
    if (parsedData) {
      onTransactionParsed(parsedData);
      onClose();
    }
  };

  const handleEdit = (field, value) => {
    setParsedData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  if (!isSupported) {
    return (
      <div className="card p-6 max-w-md mx-auto">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            Speech Recognition Not Supported
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Your browser doesn't support speech recognition. Please use a modern browser like Chrome or Edge.
          </p>
          <button onClick={onClose} className="btn-secondary">
            Close
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="card p-6 max-w-lg mx-auto">
      <div className="text-center mb-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
          Voice Input
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          Say something like: "Spent 50 on food today" or "Earned 1000 from salary"
        </p>
      </div>

      {/* Voice Control */}
      <div className="text-center mb-6">
        <button
          onClick={isListening ? stopListening : startListening}
          className={`relative p-4 rounded-full transition-all ${
            isListening 
              ? 'bg-red-500 hover:bg-red-600 voice-recording' 
              : 'bg-primary-500 hover:bg-primary-600'
          }`}
        >
          {isListening ? (
            <MicOff className="h-8 w-8 text-white" />
          ) : (
            <Mic className="h-8 w-8 text-white" />
          )}
        </button>
        <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
          {isListening ? 'Listening... Click to stop' : 'Click to start listening'}
        </p>
      </div>

      {/* Transcript */}
      {transcript && (
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            What you said:
          </label>
          <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
            <p className="text-gray-900 dark:text-white">{transcript}</p>
          </div>
        </div>
      )}

      {/* Parsed Data */}
      {parsedData && (
        <div className="space-y-4 mb-6">
          <h4 className="font-medium text-gray-900 dark:text-white">Parsed Transaction:</h4>
          
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Type
              </label>
              <select
                value={parsedData.type}
                onChange={(e) => handleEdit('type', e.target.value)}
                className="input w-full"
              >
                <option value="expense">Expense</option>
                <option value="income">Income</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Amount
              </label>
              <input
                type="number"
                step="0.01"
                value={parsedData.amount}
                onChange={(e) => handleEdit('amount', parseFloat(e.target.value))}
                className="input w-full"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Category
            </label>
            <input
              type="text"
              value={parsedData.category}
              onChange={(e) => handleEdit('category', e.target.value)}
              className="input w-full"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Description
            </label>
            <input
              type="text"
              value={parsedData.description}
              onChange={(e) => handleEdit('description', e.target.value)}
              className="input w-full"
            />
          </div>
        </div>
      )}

      {/* Actions */}
      <div className="flex justify-end space-x-3">
        <button onClick={onClose} className="btn-secondary">
          Cancel
        </button>
        {parsedData && (
          <button onClick={handleConfirm} className="btn-primary">
            Add Transaction
          </button>
        )}
      </div>
    </div>
  );
};

export default VoiceInput;
