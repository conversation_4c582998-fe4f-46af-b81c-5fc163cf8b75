import { useState, useEffect } from 'react'
import axios from 'axios'
import TransactionForm from './components/TransactionForm'
import TransactionList from './components/TransactionList'
import './App.css'

function App() {
  const [transactions, setTransactions] = useState([])
  const [balance, setBalance] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [editingTransaction, setEditingTransaction] = useState(null)

  // Fetch transactions from API
  const fetchTransactions = async () => {
    try {
      setLoading(true)
      const response = await axios.get('/api/transactions')
      if (response.data.success) {
        setTransactions(response.data.data.transactions)
        setBalance(response.data.data.balance)
        setError(null)
      }
    } catch (err) {
      setError('Failed to fetch transactions')
      console.error('API Error:', err)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchTransactions()
  }, [])

  // Add new transaction
  const handleAddTransaction = async (transactionData) => {
    try {
      const response = await axios.post('/api/transactions', transactionData)
      if (response.data.success) {
        setTransactions(prev => [response.data.data.transaction, ...prev])
        setBalance(response.data.data.balance)
      }
    } catch (err) {
      console.error('Error adding transaction:', err)
      alert('Failed to add transaction')
    }
  }

  // Update transaction
  const handleUpdateTransaction = async (transactionData) => {
    try {
      const response = await axios.put(`/api/transactions/${editingTransaction._id}`, transactionData)
      if (response.data.success) {
        setTransactions(prev =>
          prev.map(t => t._id === editingTransaction._id ? response.data.data.transaction : t)
        )
        setBalance(response.data.data.balance)
        setEditingTransaction(null)
      }
    } catch (err) {
      console.error('Error updating transaction:', err)
      alert('Failed to update transaction')
    }
  }

  // Delete transaction
  const handleDeleteTransaction = async (id) => {
    try {
      const response = await axios.delete(`/api/transactions/${id}`)
      if (response.data.success) {
        setTransactions(prev => prev.filter(t => t._id !== id))
        setBalance(response.data.data.balance)
      }
    } catch (err) {
      console.error('Error deleting transaction:', err)
      alert('Failed to delete transaction')
    }
  }

  // Start editing transaction
  const handleEditTransaction = (transaction) => {
    setEditingTransaction(transaction)
  }

  // Cancel editing
  const handleCancelEdit = () => {
    setEditingTransaction(null)
  }

  return (
    <div className="App">
      <header className="App-header">
        <h1>🏦 Expense Tracker</h1>
        <p>Track your income and expenses with ease</p>

        {error && (
          <div className="error-banner">
            <p>⚠️ {error}</p>
            <button onClick={fetchTransactions}>Retry</button>
          </div>
        )}

        <TransactionForm
          onSubmit={editingTransaction ? handleUpdateTransaction : handleAddTransaction}
          editingTransaction={editingTransaction}
          onCancel={handleCancelEdit}
        />

        <TransactionList
          transactions={transactions}
          onDelete={handleDeleteTransaction}
          onEdit={handleEditTransaction}
          balance={balance}
          loading={loading}
        />
      </header>
    </div>
  )
}

export default App
