import { useState, useEffect } from 'react'
import axios from 'axios'
import './App.css'

function App() {
  const [apiData, setApiData] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true)
        const response = await axios.get('/api/test')
        setApiData(response.data)
        setError(null)
      } catch (err) {
        setError('Failed to connect to backend API')
        console.error('API Error:', err)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  const testApiConnection = async () => {
    try {
      setLoading(true)
      const response = await axios.get('/api/health')
      alert(`API Health Check: ${response.data.status}\nDatabase: ${response.data.database}`)
    } catch (err) {
      alert('Failed to connect to API')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="App">
      <header className="App-header">
        <h1>🏦 Expense Tracker</h1>
        <p>MERN Stack Application</p>
        
        <div className="status-section">
          <h2>Backend Connection Status</h2>
          {loading && <p>Loading...</p>}
          {error && <p className="error">{error}</p>}
          {apiData && (
            <div className="success">
              <p>✅ Successfully connected to backend!</p>
              <div className="api-data">
                <h3>API Response:</h3>
                <pre>{JSON.stringify(apiData, null, 2)}</pre>
              </div>
            </div>
          )}
        </div>

        <div className="actions">
          <button onClick={testApiConnection} disabled={loading}>
            Test API Health
          </button>
        </div>

        <div className="tech-stack">
          <h3>Tech Stack</h3>
          <ul>
            <li>Frontend: React + Vite</li>
            <li>Backend: Express.js</li>
            <li>Database: MongoDB + Mongoose</li>
            <li>Proxy: Vite dev server proxy</li>
          </ul>
        </div>
      </header>
    </div>
  )
}

export default App
