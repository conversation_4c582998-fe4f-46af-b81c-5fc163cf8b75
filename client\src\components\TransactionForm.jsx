import { useState } from 'react'
import './TransactionForm.css'

const TransactionForm = ({ onSubmit, editingTransaction, onCancel }) => {
  const [formData, setFormData] = useState({
    amount: editingTransaction?.amount || '',
    category: editingTransaction?.category || '',
    description: editingTransaction?.description || '',
    date: editingTransaction?.date ? new Date(editingTransaction.date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
    type: editingTransaction?.type || 'expense'
  })
  const [loading, setLoading] = useState(false)
  const [errors, setErrors] = useState({})

  const categories = {
    income: [
      { value: 'salary', label: '💼 Salary' },
      { value: 'freelance', label: '💻 Freelance' },
      { value: 'business', label: '🏢 Business' },
      { value: 'investment', label: '📈 Investment' },
      { value: 'gift', label: '🎁 Gift' },
      { value: 'other-income', label: '💰 Other Income' }
    ],
    expense: [
      { value: 'food', label: '🍔 Food & Dining' },
      { value: 'transportation', label: '🚗 Transportation' },
      { value: 'housing', label: '🏠 Housing' },
      { value: 'utilities', label: '⚡ Utilities' },
      { value: 'healthcare', label: '🏥 Healthcare' },
      { value: 'entertainment', label: '🎬 Entertainment' },
      { value: 'shopping', label: '🛍️ Shopping' },
      { value: 'education', label: '📚 Education' },
      { value: 'travel', label: '✈️ Travel' },
      { value: 'other-expense', label: '💸 Other Expense' }
    ]
  }

  const handleChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value,
      // Reset category when type changes
      ...(name === 'type' && { category: '' })
    }))
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }))
    }
  }

  const validateForm = () => {
    const newErrors = {}
    
    if (!formData.amount || parseFloat(formData.amount) <= 0) {
      newErrors.amount = 'Please enter a valid amount greater than 0'
    }
    
    if (!formData.category) {
      newErrors.category = 'Please select a category'
    }
    
    if (!formData.description.trim()) {
      newErrors.description = 'Please enter a description'
    }
    
    if (!formData.date) {
      newErrors.date = 'Please select a date'
    }
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    if (!validateForm()) return
    
    setLoading(true)
    try {
      await onSubmit({
        ...formData,
        amount: parseFloat(formData.amount)
      })
      
      // Reset form if not editing
      if (!editingTransaction) {
        setFormData({
          amount: '',
          category: '',
          description: '',
          date: new Date().toISOString().split('T')[0],
          type: 'expense'
        })
      }
    } catch (error) {
      console.error('Form submission error:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="transaction-form-container">
      <h2>{editingTransaction ? 'Edit Transaction' : 'Add New Transaction'}</h2>
      
      <form onSubmit={handleSubmit} className="transaction-form">
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="type">Type *</label>
            <select
              id="type"
              name="type"
              value={formData.type}
              onChange={handleChange}
              className={errors.type ? 'error' : ''}
            >
              <option value="expense">💸 Expense</option>
              <option value="income">💰 Income</option>
            </select>
            {errors.type && <span className="error-message">{errors.type}</span>}
          </div>

          <div className="form-group">
            <label htmlFor="amount">Amount *</label>
            <input
              type="number"
              id="amount"
              name="amount"
              value={formData.amount}
              onChange={handleChange}
              placeholder="0.00"
              step="0.01"
              min="0.01"
              className={errors.amount ? 'error' : ''}
            />
            {errors.amount && <span className="error-message">{errors.amount}</span>}
          </div>
        </div>

        <div className="form-row">
          <div className="form-group">
            <label htmlFor="category">Category *</label>
            <select
              id="category"
              name="category"
              value={formData.category}
              onChange={handleChange}
              className={errors.category ? 'error' : ''}
            >
              <option value="">Select a category</option>
              {categories[formData.type].map(cat => (
                <option key={cat.value} value={cat.value}>
                  {cat.label}
                </option>
              ))}
            </select>
            {errors.category && <span className="error-message">{errors.category}</span>}
          </div>

          <div className="form-group">
            <label htmlFor="date">Date *</label>
            <input
              type="date"
              id="date"
              name="date"
              value={formData.date}
              onChange={handleChange}
              className={errors.date ? 'error' : ''}
            />
            {errors.date && <span className="error-message">{errors.date}</span>}
          </div>
        </div>

        <div className="form-group">
          <label htmlFor="description">Description *</label>
          <input
            type="text"
            id="description"
            name="description"
            value={formData.description}
            onChange={handleChange}
            placeholder="Enter description..."
            maxLength="200"
            className={errors.description ? 'error' : ''}
          />
          {errors.description && <span className="error-message">{errors.description}</span>}
          <small className="char-count">{formData.description.length}/200</small>
        </div>

        <div className="form-actions">
          <button
            type="submit"
            disabled={loading}
            className="submit-btn"
          >
            {loading ? 'Saving...' : (editingTransaction ? 'Update Transaction' : 'Add Transaction')}
          </button>
          
          {editingTransaction && (
            <button
              type="button"
              onClick={onCancel}
              className="cancel-btn"
              disabled={loading}
            >
              Cancel
            </button>
          )}
        </div>
      </form>
    </div>
  )
}

export default TransactionForm
