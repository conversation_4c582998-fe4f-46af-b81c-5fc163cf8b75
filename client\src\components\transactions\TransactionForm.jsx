import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { Plus, DollarSign, Calendar, FileText, Tag } from 'lucide-react';
import toast from 'react-hot-toast';

const TransactionForm = ({ onSubmit, editingTransaction, onCancel }) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const {
    register,
    handleSubmit,
    watch,
    reset,
    formState: { errors }
  } = useForm({
    defaultValues: editingTransaction || {
      type: 'expense',
      amount: '',
      category: '',
      description: '',
      date: new Date().toISOString().split('T')[0]
    }
  });

  const watchType = watch('type');

  const categories = {
    income: [
      { value: 'salary', label: '💼 Salary', color: 'bg-green-100 text-green-800' },
      { value: 'freelance', label: '💻 Freelance', color: 'bg-blue-100 text-blue-800' },
      { value: 'business', label: '🏢 Business', color: 'bg-purple-100 text-purple-800' },
      { value: 'investment', label: '📈 Investment', color: 'bg-indigo-100 text-indigo-800' },
      { value: 'gift', label: '🎁 Gift', color: 'bg-pink-100 text-pink-800' },
      { value: 'other-income', label: '💰 Other Income', color: 'bg-gray-100 text-gray-800' }
    ],
    expense: [
      { value: 'food', label: '🍔 Food & Dining', color: 'bg-red-100 text-red-800' },
      { value: 'transportation', label: '🚗 Transportation', color: 'bg-yellow-100 text-yellow-800' },
      { value: 'housing', label: '🏠 Housing', color: 'bg-orange-100 text-orange-800' },
      { value: 'utilities', label: '⚡ Utilities', color: 'bg-cyan-100 text-cyan-800' },
      { value: 'healthcare', label: '🏥 Healthcare', color: 'bg-teal-100 text-teal-800' },
      { value: 'entertainment', label: '🎬 Entertainment', color: 'bg-violet-100 text-violet-800' },
      { value: 'shopping', label: '🛍️ Shopping', color: 'bg-rose-100 text-rose-800' },
      { value: 'education', label: '📚 Education', color: 'bg-emerald-100 text-emerald-800' },
      { value: 'travel', label: '✈️ Travel', color: 'bg-sky-100 text-sky-800' },
      { value: 'other-expense', label: '💸 Other Expense', color: 'bg-gray-100 text-gray-800' }
    ]
  };

  const onFormSubmit = async (data) => {
    setIsSubmitting(true);
    try {
      await onSubmit({
        ...data,
        amount: parseFloat(data.amount)
      });
      
      if (!editingTransaction) {
        reset();
        toast.success('Transaction added successfully!');
      } else {
        toast.success('Transaction updated successfully!');
      }
    } catch (error) {
      toast.error('Failed to save transaction');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="card p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
          {editingTransaction ? 'Edit Transaction' : 'Add New Transaction'}
        </h2>
        {editingTransaction && (
          <button
            type="button"
            onClick={onCancel}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            Cancel
          </button>
        )}
      </div>

      <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-6">
        {/* Type Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            Transaction Type
          </label>
          <div className="grid grid-cols-2 gap-3">
            <label className="relative">
              <input
                {...register('type', { required: 'Transaction type is required' })}
                type="radio"
                value="expense"
                className="sr-only peer"
              />
              <div className="flex items-center justify-center p-4 border-2 border-gray-200 rounded-lg cursor-pointer peer-checked:border-red-500 peer-checked:bg-red-50 dark:border-gray-600 dark:peer-checked:border-red-400 dark:peer-checked:bg-red-900/20 transition-all">
                <span className="text-red-600 dark:text-red-400 font-medium">💸 Expense</span>
              </div>
            </label>
            <label className="relative">
              <input
                {...register('type', { required: 'Transaction type is required' })}
                type="radio"
                value="income"
                className="sr-only peer"
              />
              <div className="flex items-center justify-center p-4 border-2 border-gray-200 rounded-lg cursor-pointer peer-checked:border-green-500 peer-checked:bg-green-50 dark:border-gray-600 dark:peer-checked:border-green-400 dark:peer-checked:bg-green-900/20 transition-all">
                <span className="text-green-600 dark:text-green-400 font-medium">💰 Income</span>
              </div>
            </label>
          </div>
          {errors.type && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.type.message}</p>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Amount */}
          <div>
            <label htmlFor="amount" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Amount
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <DollarSign className="h-5 w-5 text-gray-400" />
              </div>
              <input
                {...register('amount', {
                  required: 'Amount is required',
                  min: { value: 0.01, message: 'Amount must be greater than 0' }
                })}
                type="number"
                step="0.01"
                min="0.01"
                className={`input pl-10 ${errors.amount ? 'border-red-500 focus:ring-red-500' : ''}`}
                placeholder="0.00"
              />
            </div>
            {errors.amount && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.amount.message}</p>
            )}
          </div>

          {/* Date */}
          <div>
            <label htmlFor="date" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Date
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Calendar className="h-5 w-5 text-gray-400" />
              </div>
              <input
                {...register('date', { required: 'Date is required' })}
                type="date"
                className={`input pl-10 ${errors.date ? 'border-red-500 focus:ring-red-500' : ''}`}
              />
            </div>
            {errors.date && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.date.message}</p>
            )}
          </div>
        </div>

        {/* Category */}
        <div>
          <label htmlFor="category" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Category
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Tag className="h-5 w-5 text-gray-400" />
            </div>
            <select
              {...register('category', { required: 'Category is required' })}
              className={`input pl-10 ${errors.category ? 'border-red-500 focus:ring-red-500' : ''}`}
            >
              <option value="">Select a category</option>
              {categories[watchType]?.map((cat) => (
                <option key={cat.value} value={cat.value}>
                  {cat.label}
                </option>
              ))}
            </select>
          </div>
          {errors.category && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.category.message}</p>
          )}
        </div>

        {/* Description */}
        <div>
          <label htmlFor="description" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Description
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <FileText className="h-5 w-5 text-gray-400" />
            </div>
            <input
              {...register('description', {
                required: 'Description is required',
                maxLength: { value: 200, message: 'Description cannot exceed 200 characters' }
              })}
              type="text"
              className={`input pl-10 ${errors.description ? 'border-red-500 focus:ring-red-500' : ''}`}
              placeholder="Enter description..."
              maxLength={200}
            />
          </div>
          {errors.description && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.description.message}</p>
          )}
        </div>

        {/* Submit Button */}
        <div className="flex justify-end space-x-3">
          {editingTransaction && (
            <button
              type="button"
              onClick={onCancel}
              className="btn-secondary px-4 py-2"
              disabled={isSubmitting}
            >
              Cancel
            </button>
          )}
          <button
            type="submit"
            disabled={isSubmitting}
            className="btn-primary px-6 py-2 flex items-center"
          >
            {isSubmitting ? (
              <div className="flex items-center">
                <div className="spinner h-4 w-4 mr-2"></div>
                {editingTransaction ? 'Updating...' : 'Adding...'}
              </div>
            ) : (
              <>
                <Plus className="h-4 w-4 mr-2" />
                {editingTransaction ? 'Update Transaction' : 'Add Transaction'}
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default TransactionForm;
