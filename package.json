{"name": "splitify", "version": "1.0.0", "description": "Full-stack MERN expense tracker and splitter with real-time collaboration", "main": "server/index.js", "scripts": {"dev": "concurrently \"npm run server\" \"npm run client\"", "server": "cd server && npm run dev", "client": "cd client && npm run dev", "install-server": "cd server && npm install", "install-client": "cd client && npm install", "install-all": "npm install && npm run install-server && npm run install-client", "build": "cd client && npm run build", "start": "cd server && npm start", "deploy": "npm run build && cd server && npm start"}, "keywords": ["mern", "expense-tracker", "bill-splitter", "react", "express", "mongodb", "socket.io", "real-time"], "author": "", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}