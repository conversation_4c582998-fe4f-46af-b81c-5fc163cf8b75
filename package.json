{"name": "expense-tracker-mern", "version": "1.0.0", "description": "MERN stack expense tracker application", "main": "server/index.js", "scripts": {"dev": "concurrently \"npm run server\" \"npm run client\"", "server": "cd server && npm run dev", "client": "cd client && npm run dev", "install-server": "cd server && npm install", "install-client": "cd client && npm install", "install-all": "npm install && npm run install-server && npm run install-client", "build": "cd client && npm run build", "start": "cd server && npm start"}, "keywords": ["mern", "expense-tracker", "react", "express", "mongodb"], "author": "", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}